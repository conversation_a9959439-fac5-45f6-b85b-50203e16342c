using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Abp.Application.Services.Dto;
using Abp.Domain.Entities;
using Abp.Domain.Repositories;
using Abp.Domain.Uow;
using Abp.Runtime.Session;
using Abp.UI;
using aibase.DrillHoleEntity;
using aibase.DrillHoles.Dto;
using aibase.DrillHoles.Services.DrillHoleService;
using aibase.ImageCrops.Dto;
using aibase.ImageEntity;
using aibase.Images.Dto;
using aibase.Images.Services.AzureService;
using aibase.Images.Services.CropService;
using aibase.Images.Services.UploadService.Handler;
using aibase.ImageSubtypes;
using aibase.ImageSubtypes.Dto;
using aibase.ImageTypes;
using aibase.ImageTypes.Dto;
using aibase.Models.Dto;
using aibase.ProjectEntity;
using aibase.Projects.Dto;
using aibase.Prospects;
using aibase.Prospects.Dto;
using aibase.StepWorkflowEntity;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using File = aibase.FileEntity.File;

namespace aibase.Images.Services.ImageService;

/// <inheritdoc />
public class ImageService : IImageService
{
    private readonly IRepository<Image, int> _repository;
    private readonly IRepository<File, int> _fileRepository;
    private readonly IRepository<DrillHole, int> _drillHoleRepository;
    private readonly IRepository<Prospect, int> _prospectRepository;
    private readonly IRepository<StepWorkflow, int> _stepWorkflowRepository;
    private readonly IRepository<Project, int> _projectRepository;
    private readonly IRepository<ImageType, int> _imageTypeRepository;
    private readonly IRepository<ImageSubtype, int> _imageSubtypeRepository;
    private readonly IDrillHoleService _drillHoleService;
    private readonly IAzureService _azureService;
    private readonly ICropService _cropService;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IAbpSession _abpSession;
    private readonly IMapper _mapper;

    /// <summary>
    /// 
    /// </summary>
    public ImageService(
        IRepository<Image, int> repository,
        IRepository<File, int> fileRepository,
        IRepository<DrillHole, int> drillHoleRepository,
        IRepository<Prospect, int> prospectRepository,
        IRepository<StepWorkflow, int> stepWorkflowRepository,
        IRepository<ImageType, int> imageTypeRepository,
        IRepository<ImageSubtype, int> imageSubtypeRepository,
        IRepository<Project, int> projectRepository,
        IAzureService azureService,
        ICropService cropService,
        IDrillHoleService drillHoleService,
        IUnitOfWorkManager unitOfWorkManager,
        IMapper mapper,
        IAbpSession abpSession
    )
    {
        _repository = repository;
        _fileRepository = fileRepository;
        _stepWorkflowRepository = stepWorkflowRepository;
        _drillHoleRepository = drillHoleRepository;
        _imageTypeRepository = imageTypeRepository;
        _imageSubtypeRepository = imageSubtypeRepository;
        _projectRepository = projectRepository;
        _prospectRepository = prospectRepository;
        _azureService = azureService;
        _drillHoleService = drillHoleService;
        _cropService = cropService;
        _unitOfWorkManager = unitOfWorkManager;
        _mapper = mapper;
        _abpSession = abpSession;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ImageDto>> GetAllAsync(PagedImageResultRequestDto input)
    {
        var tenantId = _abpSession.GetTenantId();
        var parsedHoleIds = ImageCommon.DeserializeIds(input.HoleIds, nameof(input.HoleIds));
        var parsedDrillHoleNames = ImageCommon.DeserializeNames(input.DrillHoleNames);

        // Optimize ignore fields parsing using HashSet for O(1) lookup
        var ignoreFieldsSet = CreateIgnoreFieldsSet(input.IgnoreFields);
        var ignoreFiles = ignoreFieldsSet.Contains("files");
        var ignoreDrillHole = ignoreFieldsSet.Contains("drillhole");
        var ignoreProject = ignoreFieldsSet.Contains("project");
        var ignoreProspect = ignoreFieldsSet.Contains("prospect");
        var ignoreBoundingBox = ignoreFieldsSet.Contains("boundingbox");
        var ignoreBoundingRows = ignoreFieldsSet.Contains("boundingrows");
        var ignoreOcrResult = ignoreFieldsSet.Contains("ocrresult");
        var ignoreDirectOcrResult = ignoreFieldsSet.Contains("directocrresult");
        var ignoreSegmentResult = ignoreFieldsSet.Contains("segmentresult");
        var ignoreSegmentDetailResult = ignoreFieldsSet.Contains("segmentdetailresult");
        var ignoreDrillholeNameOcr = ignoreFieldsSet.Contains("drillholenameoocr");
        var ignoreDepthFromOcr = ignoreFieldsSet.Contains("depthfromocr");
        var ignoreDepthToOcr = ignoreFieldsSet.Contains("depthtoocr");
        var ignoreFractures = ignoreFieldsSet.Contains("fractures");
        var ignoreCroppedImages = ignoreFieldsSet.Contains("croppedimages");
        var ignoreOriginalFractures = ignoreFieldsSet.Contains("originalfractures");
        var ignoreOriginalBoundingBox = ignoreFieldsSet.Contains("originalboundingbox");
        var ignoreOriginalBoundingRows = ignoreFieldsSet.Contains("originalboundingrows");
        var ignoreOriginalSegmentResult = ignoreFieldsSet.Contains("originalsegmentresult");
        var ignoreOriginalDrillholeNameOcr = ignoreFieldsSet.Contains("originaldrillholenameoocr");
        var ignoreOriginalDirectOcrResult = ignoreFieldsSet.Contains("originaldirectocrresult");
        var ignoreRockLines = ignoreFieldsSet.Contains("rocklines");

        // Optimize: Use single query with projection to get only needed data
        var query = QueryImageOptimized(input, !ignoreFiles, !ignoreCroppedImages, !ignoreRockLines,
            ignoreDrillHole, ignoreProject, ignoreProspect);

        // Get total count efficiently
        var totalCount = await query.CountAsync();

        // Get paginated results with all related data in single query
        var images = await query
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToListAsync();

        // Optimize DTO mapping with parallel processing for large datasets
        var imageDtos = images.AsParallel().Select(x => CreateImageDto(x,
            ignoreFiles, ignoreDrillHole, ignoreProject, ignoreProspect, ignoreBoundingBox,
            ignoreBoundingRows, ignoreOcrResult, ignoreDirectOcrResult, ignoreSegmentResult,
            ignoreSegmentDetailResult, ignoreDrillholeNameOcr, ignoreDepthFromOcr, ignoreDepthToOcr,
            ignoreFractures, ignoreCroppedImages, ignoreOriginalFractures, ignoreOriginalBoundingBox,
            ignoreOriginalBoundingRows, ignoreOriginalSegmentResult, ignoreOriginalDrillholeNameOcr,
            ignoreOriginalDirectOcrResult)).ToList();

        return new PagedResultDto<ImageDto>(totalCount, imageDtos);
    }

    /// <inheritdoc />
    public async Task<ImageDto> GetAsync(EntityDto<int> input)
    {
        var imageDto = await _repository
            .GetAllIncluding(
                x => x.Project,
                x => x.Prospect,
                x => x.DrillHole,
                x => x.Files,
                x => x.CroppedImages,
                x => x.ImageType,
                x => x.ImageSubtype)
            .FirstOrDefaultAsync(x => x.Id == input.Id);

        if (imageDto == null)
        {
            throw new EntityNotFoundException(typeof(Image), input.Id);
        }

        var image = _mapper.Map<ImageDto>(imageDto);

        if (image.CroppedImages.Count > 0)
        {
            image.CroppedImages = image.CroppedImages
                .OrderBy(crop => crop.DepthFrom ?? double.MaxValue)
                .ToList();
        }

        return image;
    }

    /// <inheritdoc />
    public async Task<GetResultImageDto> GetResultImageAsync(int id)
    {
        var image = await _repository.GetAllIncluding(x => x.CroppedImages).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == id);

        return new GetResultImageDto
        {
            ImageCrops = _mapper.Map<List<ImageCropDto>>(image?.CroppedImages),
            ImageOcrs = image?.OcrResult,
            ImageSegments = image?.SegmentResult
        };
    }

    /// <inheritdoc />
    public async Task DeleteMultiAsync(string ids)
    {
        var imagesIds = Array.Empty<int>();
        if (!string.IsNullOrEmpty(ids))
        {
            try
            {
                imagesIds = JsonConvert.DeserializeObject<int[]>(ids) ?? [];
            }
            catch (JsonException ex)
            {
                throw new ArgumentException("Invalid ids format", nameof(ids), ex);
            }
        }

        foreach (var imageId in imagesIds)
        {
            var image = await _repository.GetAllIncluding(x => x.Files)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == imageId);
            var files = image?.Files.ToList() ?? [];

            using var transaction = _unitOfWorkManager.Begin();
            await _repository.DeleteAsync(imageId);

            foreach (var file in files)
            {
                await _fileRepository.DeleteAsync(file.Id);
                await _azureService.DeleteFileAsync(file.FileName);
            }

            await _unitOfWorkManager.Current.SaveChangesAsync();
            if (image != null) await _drillHoleService.UpdateTotalImageByDrillholeAsync(image.DrillHoleId);

            await transaction.CompleteAsync();
        }
    }

    /// <inheritdoc />
    public async Task<List<string>> ValidateImageNameAsync(ValidateDto validateDto)
    {
        if (validateDto.Names.Count == 0)
        {
            return [];
        }

        // Get all file names for the project in a single query
        var existingFileNames = await _repository.GetAll()
            .Where(img => img.ProjectId == validateDto.ProjectId)
            .Join(
                _fileRepository.GetAll(),
                img => img.Id,
                file => file.ImageId,
                (img, file) => file.FileName
            )
            .AsNoTracking()
            .ToListAsync();

        // Convert input names to filename without extension and check for duplicates
        return validateDto.Names
            .Where(name => existingFileNames.Contains(
                Path.GetFileNameWithoutExtension(name)))
            .ToList();
    }

    /// <inheritdoc />
    public async Task<string> UploadImgAsync(UploadImageDto upload)
    {
        var uploadedFile = upload.Image;
        var fileName = Path.GetFileNameWithoutExtension(uploadedFile.FileName);

        await using var stream = uploadedFile.OpenReadStream();
        var fileUrl = await _azureService.UploadFileAsync(stream, fileName, true);

        return fileUrl;
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<ImageCropDto>> CropImgAsync(CropCoordinateDto input)
    {
        var imageCrops = await _cropService.CropImg(input.Id, input.CropCoordinate, false);

        var imageCropDto = imageCrops.Select(cropped => new ImageCropDto
        {
            Id = cropped.Id,
            ImageId = cropped.ImageId,
            UrlCroppedImage = cropped.UrlCroppedImage,
            MediumSize = cropped.MediumSize,
            Type = cropped.Type,
        }).ToList();

        return new PagedResultDto<ImageCropDto>(imageCropDto.Count, imageCropDto);
    }

    /// <inheritdoc />
    public async Task UpdateStatusAsync(int id, int status)
    {
        var image = await _repository.GetAsync(id);
        if (image == null)
        {
            throw new KeyNotFoundException("Image not found");
        }

        image.ImageStatus = (ImageStatus)status;
        await _repository.UpdateAsync(image);
    }

    /// <inheritdoc />
    public async Task UpdateResultOcrSegmentAsync(UpdateResultOcrSegment input)
    {
        var image = await _repository.GetAsync(input.Id);
        if (image == null)
        {
            throw new KeyNotFoundException("Image not found");
        }

        try
        {
            var ocrResults = JsonConvert.DeserializeObject<List<OcrResultV2Dto>>(input.Ocr ?? "[]")
                             ?? [];
            if (ocrResults.Any(r => string.IsNullOrWhiteSpace(r.text) || r.x == 0 || r.y == 0))
            {
                throw new UserFriendlyException("Coordinates and values of the OCR are required.");
            }
        }
        catch (JsonException)
        {
            throw new UserFriendlyException("Coordinates and values of the OCR are required.");
        }

        image.OcrResult = input.Ocr ?? image.OcrResult;
        image.DirectOcrResult = input.DirectOcr ?? image.DirectOcrResult;
        image.SegmentResult = input.Segment ?? image.SegmentResult;
        await _repository.UpdateAsync(image);
    }

    /// <inheritdoc />
    public async Task UpdateImageAsync(UpdateImageDto input)
    {
        var image = await _repository.GetAsync(input.Id);
        if (image == null)
        {
            throw new KeyNotFoundException("Image not found");
        }

        var isProjectIdUpdated = input.ProjectId.HasValue && input.ProjectId != image.ProjectId;
        var isProspectIdUpdated = input.ProspectId.HasValue && input.ProspectId != image.ProspectId;
        var isDrillHoleIdUpdated = input.DrillHoleId.HasValue && input.DrillHoleId != image.DrillHoleId;

        var newProjectId = input.ProjectId ?? image.ProjectId;
        var newProspectId = input.ProspectId ?? image.ProspectId;
        var newDrillHoleId = input.DrillHoleId ?? image.DrillHoleId;

        if (isProjectIdUpdated || isProspectIdUpdated || isDrillHoleIdUpdated)
        {
            var prospect = await _prospectRepository
                .FirstOrDefaultAsync(x => x.Id == image.ProspectId && x.ProjectId == image.ProjectId);
            if (prospect == null)
            {
                throw new UserFriendlyException(
                    $"Prospect {image.ProspectId} does not exist or does not belong to Project {image.ProjectId}");
            }

            var drillHole = await _drillHoleRepository
                .FirstOrDefaultAsync(x =>
                    x.Id == newDrillHoleId && x.ProspectId == newProspectId && x.ProjectId == newProjectId);
            if (drillHole == null)
            {
                throw new UserFriendlyException(
                    $"DrillHole {newDrillHoleId} does not exist or does not belong to Prospect {newProspectId} or Project {newProjectId}");
            }
        }

        image.ProjectId = newProjectId;
        image.ProspectId = newProspectId;
        image.DrillHoleId = newDrillHoleId;
        image.BoundingBox = input.BoundingBox ?? image.BoundingBox;
        image.BoundingRows = input.BoundingRows ?? image.BoundingRows;
        image.ImageStatus = input.Status ?? image.ImageStatus;
        image.DepthFrom = input.DepthFrom ?? image.DepthFrom;
        image.DepthTo = input.DepthTo ?? image.DepthTo;
        image.Type = input.Type ?? image.Type;
        image.ImageClass = input.ImageClass ?? image.ImageClass;
        image.StandardType = input.StandardType ?? image.StandardType;
        image.ImageCategory = input.ImageCategory ?? image.ImageCategory;
        image.ImageTypeId = input.ImageTypeId ?? image.ImageTypeId;
        image.ImageSubtypeId = input.ImageSubtypeId ?? image.ImageSubtypeId;

        await _repository.UpdateAsync(image);
    }

    /// <inheritdoc />
    public async Task ConfirmDrillholeOcrAsync(ConfirmDrillholeOcrDto input)
    {
        var image = await _repository.GetAsync(input.ImageId);
        if (image?.DrillholeNameOcr == null && input.DrillholeName == null)
        {
            throw new UserFriendlyException(
                "Enter drill hole name or ORC to retrieve the drill hole name.");
        }

        var existingDrillHole = await _drillHoleRepository.FirstOrDefaultAsync(d =>
            d.ProjectId == input.ProjectId && image != null && d.ProspectId == image.ProspectId && d.Name ==
            (input.DrillholeName ?? image.DrillholeNameOcr));
        if (existingDrillHole == null)
        {
            var newDrillHole = new DrillHole
            {
                Name = (input.DrillholeName ?? image?.DrillholeNameOcr) ?? "",
                DrillHoleStatus = DrillHoleStatus.NotStarted,
                MaxDepth = 0,
                ProjectId = input.ProjectId,
                ProspectId = input.ProspectId,
                TenantId = _abpSession.GetTenantId()
            };

            await _drillHoleRepository.InsertAsync(newDrillHole);
            await _unitOfWorkManager.Current.SaveChangesAsync();

            image.DrillHoleId = newDrillHole.Id;
        }
        else
        {
            image.DrillHoleId = existingDrillHole.Id;
        }

        image.ImageStatus = ImageStatus.NotStarted;
        image.DepthFrom = (double)(input.DepthFrom ?? image.DepthFromOcr);
        image.DepthTo = (double)(input.DepthTo ?? image.DepthToOcr);
        image.ImageStatus = input.Status ?? image.ImageStatus;

        await _repository.UpdateAsync(image);
        await _unitOfWorkManager.Current.SaveChangesAsync();
    }

    /// <inheritdoc />
    public async Task CheckWorkflowOcrAiNamingAsync(CheckWorkflowOcr input)
    {
        var steps = await _stepWorkflowRepository.GetAll().Where(x => x.WorkflowId == input.WorkflowId)
            .ToListAsync();

        if (steps.All(x => x.ModelId != AiModelType.PredictDrillholeAndDepth))
        {
            throw new UserFriendlyException(
                "For AI Naming, you need to select a workflow that includes the Predict Drillhole step.");
        }
    }

    /// <inheritdoc />
    public async Task<int> GetSkipCountByDepthAsync(PagedImageResultRequestDto input)
    {
        if (input.Depth == null || string.IsNullOrEmpty(input.HoleIds))
        {
            throw new UserFriendlyException(
                "The DrillHoleId and The Depth parameters are required.");
        }

        var query = QueryImage(input, false, false, false)
            .Where(x => x.DepthFrom < input.Depth);

        return await query.CountAsync();
    }

    /// <inheritdoc />
    public async Task<PagedResultDto<DetailByRowDto>> GetDetailByRowAsync(PagedDetailByRowResultRequestDto input)
    {
        // Fetch images with their related entities
        var tenantId = _abpSession.GetTenantId();
        var query = _repository
            .GetAllIncluding(x => x.Project, x => x.Prospect, x => x.DrillHole)
            .Include(x => x.CroppedImages)
            .ThenInclude(x => x.RockLines)
            .AsNoTracking()
            .Where(x => x.DrillHole.TenantId == tenantId && x.ProjectId == input.ProjectId);

        if (input.ProspectId.HasValue)
        {
            query = query.Where(x => x.ProspectId == input.ProspectId);
        }

        if (input.DrillHoleId.HasValue)
        {
            query = query.Where(x => x.DrillHoleId == input.DrillHoleId);
        }
        else if (!string.IsNullOrWhiteSpace(input.DrillHoleName))
        {
            var searchName = input.DrillHoleName.ToLower();
            query = query.Where(x => x.DrillHole.Name.ToLower().Contains(searchName));
        }

        var images = await query.ToListAsync();

        // Prepare the results with client-side processing
        var detailByRows = images.SelectMany(image =>
            {
                // Filter and order crops
                var crops = image.CroppedImages
                    .Where(x => x.Type is ImageConstSettings.ImageCropRow or ImageConstSettings.ImageCropRowLower)
                    .OrderBy(x => x.DepthFrom)
                    .ToList();

                // Deserialize results
                var ocrResults = DeserializeOcrResults(image.OcrResult ?? "[]");
                var segmentResults = DeserializeSegmentResults(image.SegmentResult ?? "[]");
                var segmentDetails = DeserializeSegmentResults(image.SegmentDetailResult ?? "[]");

                var segmentCores = segmentResults
                    .Where(x => x.Class.Equals("core", StringComparison.CurrentCultureIgnoreCase)).ToList();
                var segmentBlocks = segmentResults
                    .Where(x => x.Class.Equals("block", StringComparison.CurrentCultureIgnoreCase)).ToList();

                // Create detail by rows
                return crops.Select((crop, index) => new DetailByRowDto
                {
                    ProjectName = image.Project.Name,
                    ProspectName = image.Prospect.Name,
                    DrillHoleName = image.DrillHole.Name,
                    ImageId = image.Id,
                    // CropPolygon = crop.UrlCroppedImage,
                    Ocrs = ocrResults
                        .Where(ocr => ocr.rowIndex == index)
                        .Select(ocr => new OcrResultV2Dto
                        {
                            id = ocr.id,
                            type = ocr.type,
                            x = ocr.x,
                            originalX = ocr.x,
                            y = ocr.y,
                            width = ocr.width,
                            height = ocr.height,
                            text = ocr.text,
                            probability = ocr.probability,
                            rowIndex = ocr.rowIndex,
                            ImageCropId = crop.Id
                        }).ToList(),
                    CoreOutlines = segmentCores
                        .Where(x => x.rowIndex == index)
                        .ToList(),
                    BlockOutlines = segmentBlocks
                        .Where(x => x.rowIndex == index)
                        .ToList(),
                    CorePieces = segmentDetails
                        .Where(x => x.rowIndex == index)
                        .ToList(),
                    BoxPolygon = image.BoundingBox?.ToLower() ?? string.Empty,
                    RowPolygon = crop.Coordinate?.ToLower() ?? string.Empty,
                    DepthFrom = crop.DepthFrom,
                    DepthTo = crop.DepthTo,
                });
            })
            .OrderBy(x => x.DrillHoleName)
            .ThenBy(x => x.ProspectName)
            .ThenBy(x => x.ImageId)
            .ThenBy(x => x.DepthFrom)
            .ToList();

        // Perform pagination
        var totalImageCrop = detailByRows.Count;
        var pagedResults = detailByRows
            .Skip(input.SkipCount)
            .Take(input.MaxResultCount)
            .ToList();

        return new PagedResultDto<DetailByRowDto>(totalImageCrop, pagedResults);
    }

    /// <inheritdoc />
    public async Task MockDataAsync()
    {
        using (_unitOfWorkManager.Current.DisableFilter(AbpDataFilters.MayHaveTenant))
        {
            var projects = await _projectRepository.GetAll()
                .AsNoTracking()
                .ToListAsync();

            var imageTypesByTenant = await _imageTypeRepository.GetAll()
                .AsNoTracking()
                .ToDictionaryAsync(x => (x.TenantId, x.Name), x => x.Id);

            var imageSubtypesByTenant = await _imageSubtypeRepository.GetAll()
                .AsNoTracking()
                .ToDictionaryAsync(x => (x.TenantId, x.Name), x => x.Id);

            var imagesByProject = await _repository.GetAll()
                .Where(x => x.ImageCategory == 0)
                .GroupBy(x => x.ProjectId)
                .ToDictionaryAsync(g => g.Key, g => g.ToList());

            foreach (var project in projects)
            {
                if (!imagesByProject.TryGetValue(project.Id, out var images) || !images.Any())
                    continue;

                foreach (var image in images)
                {
                    image.ImageCategory = (ImageCategoryNew)image.ImageClass;

                    var typeName = GetImageTypeName(image.Type);
                    // Lookup image type
                    if (imageTypesByTenant.TryGetValue((project.TenantId, typeName), out var typeId))
                    {
                        image.ImageTypeId = typeId;
                    }

                    if (image.StandardType.HasValue)
                    {
                        var subtypeName = GetImageSubtypeName(image.StandardType.Value);
                        if (imageSubtypesByTenant.TryGetValue((project.TenantId, subtypeName), out var subtypeId))
                        {
                            image.ImageSubtypeId = subtypeId;
                        }
                    }
                }
            }
        }
    }

    private static string GetImageTypeName(ImageCategory category) => category switch
    {
        ImageCategory.Standard => "Standard",
        ImageCategory.Hyperspectral => "Hyperspectral",
        ImageCategory.Optical => "Optical",
        ImageCategory.AtRig => "Rig",
        _ => ""
    };

    private static string GetImageSubtypeName(StandardType type) => type switch
    {
        StandardType.Dry => "Dry",
        StandardType.Wet => "Wet",
        _ => ""
    };

    // Helper methods to deserialize JSON results
    private static List<OcrResultV2Dto> DeserializeOcrResults(string ocrResult)
    {
        try
        {
            return JsonConvert.DeserializeObject<List<OcrResultV2Dto>>(ocrResult) ?? [];
        }
        catch (Exception)
        {
            return [];
        }
    }

    private static List<SegmentResultDto> DeserializeSegmentResults(string segmentResult)
    {
        try
        {
            return JsonConvert.DeserializeObject<List<SegmentResultDto>>(segmentResult) ?? [];
        }
        catch (Exception)
        {
            return [];
        }
    }

    private IQueryable<Image> QueryImage(PagedImageResultRequestDto input, bool includeFiles, bool includeCroppedImages,
        bool includeRockLines)
    {
        ArgumentNullException.ThrowIfNull(input);

        var projectIds = ImageCommon.DeserializeIds(input.ProjectIds, nameof(input.ProjectIds));
        var prospectIds = ImageCommon.DeserializeIds(input.ProspectIds, nameof(input.ProspectIds));
        var drillHoleIds = ImageCommon.DeserializeIds(input.HoleIds, nameof(input.HoleIds));
        var drillHoleNames = ImageCommon.DeserializeNames(input.DrillHoleNames);

        var query = _repository.GetAll(); // Explicitly type as IQueryable<Image>

        query = query.Include(x => x.Project)
            .Include(x => x.Prospect)
            .Include(x => x.DrillHole)
            .Include(x => x.ImageType)
            .Include(x => x.ImageSubtype);

        if (includeFiles)
        {
            query = query.Include(x => x.Files);
        }

        if (includeCroppedImages && includeRockLines)
        {
            query = query.Include(x => x.CroppedImages).ThenInclude(x => x.RockLines);
        }

        if (includeCroppedImages && !includeRockLines)
        {
            query = query.Include(x => x.CroppedImages);
        }

        query = query.Where(x => x.DrillHole.TenantId == _abpSession.GetTenantId())
            .AsNoTracking();

        // Apply filters
        if (drillHoleIds?.Length > 0)
            query = query.Where(x => drillHoleIds.Contains(x.DrillHoleId));
        if (input.ImageClass.HasValue)
            query = query.Where(x => x.ImageClass == input.ImageClass);
        if (input.Type.HasValue)
            query = query.Where(x => x.Type == input.Type);
        if (input.ImageCategory.HasValue)
            query = query.Where(x => x.ImageCategory == input.ImageCategory);
        if (input.ImageTypeId.HasValue)
            query = query.Where(x => x.ImageTypeId == input.ImageTypeId);
        if (input.ImageSubtypeId.HasValue)
            query = query.Where(x => x.ImageSubtypeId == input.ImageSubtypeId);

        // Apply additional filters            
        if (projectIds?.Length > 0)
            query = query.Where(x => projectIds.Contains(x.ProjectId));
        if (prospectIds?.Length > 0)
            query = query.Where(x => prospectIds.Contains(x.ProspectId));
        if (input.StandardType.HasValue)
            query = query.Where(x => x.StandardType == input.StandardType);
        if (input.DepthFrom.HasValue)
            query = query.Where(x => x.DepthFrom >= input.DepthFrom);
        if (input.DepthTo.HasValue)
            query = query.Where(x => x.DepthTo <= input.DepthTo);
        // if (input.Depth.HasValue)
        //     query = query.Where(x => x.DepthFrom <= input.Depth && x.DepthTo >= input.Depth);
        if (input.ImageStatus.HasValue)
            query = query.Where(x => x.ImageStatus == input.ImageStatus);
        if (!string.IsNullOrEmpty(input.DrillHoleNames))
            query = query.Where(x =>
                drillHoleNames.Contains(x.DrillHole.Name)); // TenantId check is already applied globally

        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            query = query.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            query = query
                .OrderBy(x => x.Project.Name)
                .ThenBy(x => x.DrillHole.Name)
                .ThenBy(x => x.DepthFrom);
        }

        return query;
    }

    /// <summary>
    /// Creates a HashSet of ignore fields for O(1) lookup performance
    /// </summary>
    private static HashSet<string> CreateIgnoreFieldsSet(string? ignoreFields)
    {
        if (string.IsNullOrEmpty(ignoreFields))
            return new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        var parsedIgnoreFields = ImageCommon.DeserializeNames(ignoreFields);
        return new HashSet<string>(parsedIgnoreFields, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Optimized query method that includes only necessary related data
    /// </summary>
    private IQueryable<Image> QueryImageOptimized(PagedImageResultRequestDto input, bool includeFiles,
        bool includeCroppedImages, bool includeRockLines, bool ignoreDrillHole, bool ignoreProject, bool ignoreProspect)
    {
        ArgumentNullException.ThrowIfNull(input);

        var projectIds = ImageCommon.DeserializeIds(input.ProjectIds, nameof(input.ProjectIds));
        var prospectIds = ImageCommon.DeserializeIds(input.ProspectIds, nameof(input.ProspectIds));
        var drillHoleIds = ImageCommon.DeserializeIds(input.HoleIds, nameof(input.HoleIds));
        var drillHoleNames = ImageCommon.DeserializeNames(input.DrillHoleNames);

        var query = _repository.GetAll().AsNoTracking();

        // Only include related entities that are not ignored
        if (!ignoreDrillHole)
            query = query.Include(x => x.DrillHole);
        if (!ignoreProject)
            query = query.Include(x => x.Project);
        if (!ignoreProspect)
            query = query.Include(x => x.Prospect);

        query = query.Include(x => x.ImageType).Include(x => x.ImageSubtype);

        if (includeFiles)
            query = query.Include(x => x.Files);

        if (includeCroppedImages && includeRockLines)
            query = query.Include(x => x.CroppedImages).ThenInclude(x => x.RockLines);
        else if (includeCroppedImages)
            query = query.Include(x => x.CroppedImages);

        // Apply filters
        query = query.Where(x => x.DrillHole.TenantId == _abpSession.GetTenantId());

        if (drillHoleIds?.Length > 0)
            query = query.Where(x => drillHoleIds.Contains(x.DrillHoleId));
        if (input.ImageClass.HasValue)
            query = query.Where(x => x.ImageClass == input.ImageClass);
        if (input.Type.HasValue)
            query = query.Where(x => x.Type == input.Type);
        if (input.ImageCategory.HasValue)
            query = query.Where(x => x.ImageCategory == input.ImageCategory);
        if (input.ImageTypeId.HasValue)
            query = query.Where(x => x.ImageTypeId == input.ImageTypeId);
        if (input.ImageSubtypeId.HasValue)
            query = query.Where(x => x.ImageSubtypeId == input.ImageSubtypeId);
        if (projectIds?.Length > 0)
            query = query.Where(x => projectIds.Contains(x.ProjectId));
        if (prospectIds?.Length > 0)
            query = query.Where(x => prospectIds.Contains(x.ProspectId));
        if (input.StandardType.HasValue)
            query = query.Where(x => x.StandardType == input.StandardType);
        if (input.DepthFrom.HasValue)
            query = query.Where(x => x.DepthFrom >= input.DepthFrom);
        if (input.DepthTo.HasValue)
            query = query.Where(x => x.DepthTo <= input.DepthTo);
        if (input.ImageStatus.HasValue)
            query = query.Where(x => x.ImageStatus == input.ImageStatus);
        if (!string.IsNullOrEmpty(input.DrillHoleNames))
            query = query.Where(x => drillHoleNames.Contains(x.DrillHole.Name));

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(input.SortField) && !string.IsNullOrWhiteSpace(input.SortOrder))
        {
            query = query.ApplySorting(input.SortField, input.SortOrder, r => r.CreationTime);
        }
        else
        {
            query = query
                .OrderBy(x => x.Project.Name)
                .ThenBy(x => x.DrillHole.Name)
                .ThenBy(x => x.DepthFrom);
        }

        return query;
    }

    /// <summary>
    /// Creates ImageDto with optimized mapping
    /// </summary>
    private ImageDto CreateImageDto(Image x, bool ignoreFiles, bool ignoreDrillHole, bool ignoreProject,
        bool ignoreProspect, bool ignoreBoundingBox, bool ignoreBoundingRows, bool ignoreOcrResult,
        bool ignoreDirectOcrResult, bool ignoreSegmentResult, bool ignoreSegmentDetailResult,
        bool ignoreDrillholeNameOcr, bool ignoreDepthFromOcr, bool ignoreDepthToOcr, bool ignoreFractures,
        bool ignoreCroppedImages, bool ignoreOriginalFractures, bool ignoreOriginalBoundingBox,
        bool ignoreOriginalBoundingRows, bool ignoreOriginalSegmentResult, bool ignoreOriginalDrillholeNameOcr,
        bool ignoreOriginalDirectOcrResult)
    {
        return new ImageDto
        {
            Id = x.Id,
            files = ignoreFiles ? null : x.Files?.Select(f => new FileDto
            {
                Id = f.Id,
                Url = f.Url,
                Size = f.Size,
                Width = f.Width,
                Height = f.Height
            }).ToList() ?? [],
            ImageClass = x.ImageClass,
            StandardType = x.StandardType,
            ImageCategory = x.ImageCategory,
            ImageType = x.ImageType != null ? _mapper.Map<ImageTypeDto>(x.ImageType) : null,
            ImageSubtype = x.ImageSubtype != null ? _mapper.Map<ImageSubtypeDto>(x.ImageSubtype) : null,
            DrillHole = ignoreDrillHole ? null : (x.DrillHole != null ? _mapper.Map<DrillHoleDto>(x.DrillHole) : null),
            Project = ignoreProject ? null : (x.Project != null ? _mapper.Map<ProjectDto>(x.Project) : null),
            Prospect = ignoreProspect ? null : (x.Prospect != null ? _mapper.Map<ProspectDto>(x.Prospect) : null),
            DepthFrom = x.DepthFrom,
            DepthTo = x.DepthTo,
            ImageStatus = x.ImageStatus,
            BoundingBox = ignoreBoundingBox ? null : x.BoundingBox,
            BoundingRows = ignoreBoundingRows ? null : x.BoundingRows,
            OcrResult = ignoreOcrResult ? null : x.OcrResult,
            DirectOcrResult = ignoreDirectOcrResult ? null : x.DirectOcrResult,
            SegmentResult = ignoreSegmentResult ? null : x.SegmentResult,
            SegmentDetailResult = ignoreSegmentDetailResult ? null : x.SegmentDetailResult,
            DrillholeNameOcr = ignoreDrillholeNameOcr ? null : x.DrillholeNameOcr,
            DepthFromOcr = ignoreDepthFromOcr ? null : x.DepthFromOcr,
            DepthToOcr = ignoreDepthToOcr ? null : x.DepthToOcr,
            Fractures = ignoreFractures ? null : x.Fractures,
            CroppedImages = ignoreCroppedImages ? null : x.CroppedImages?
                .OrderBy(crop => crop.DepthFrom)
                .Select(crop => _mapper.Map<ImageCropDto>(crop))
                .ToList(),
            OriginalFractures = ignoreOriginalFractures ? null : x.OriginalFractures,
            OriginalBoundingBox = ignoreOriginalBoundingBox ? null : x.OriginalBoundingBox,
            OriginalBoundingRows = ignoreOriginalBoundingRows ? null : x.OriginalBoundingRows,
            OriginalSegmentResult = ignoreOriginalSegmentResult ? null : x.OriginalSegmentResult,
            OriginalDrillholeNameOcr = ignoreOriginalDrillholeNameOcr ? null : x.OriginalDrillholeNameOcr,
            OriginalDirectOcrResult = ignoreOriginalDirectOcrResult ? null : x.OriginalDirectOcrResult,
            CreatedByUser = x.CreatedByUser,
            CreatedByName = x.CreatedByName,
            CreationTime = x.CreationTime
        };
    }
}