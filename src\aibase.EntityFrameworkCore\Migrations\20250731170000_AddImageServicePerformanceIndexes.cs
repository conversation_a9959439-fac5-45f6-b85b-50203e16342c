using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace aibase.Migrations
{
    /// <summary>
    /// Migration to add performance indexes for ImageService GetAllAsync optimization
    /// </summary>
    public partial class AddImageServicePerformanceIndexes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Critical indexes for Images table based on GetAllAsync query patterns
            
            // Primary composite index for tenant-based queries with drill hole filtering
            migrationBuilder.CreateIndex(
                name: "IX_Images_TenantId_DrillHoleId_Performance",
                table: "AbpImages",
                columns: new[] { "DrillHoleId", "ProjectId", "ProspectId" },
                filter: null);

            // Index for depth range queries (common in geological data)
            migrationBuilder.CreateIndex(
                name: "IX_Images_DepthRange_Performance",
                table: "AbpImages",
                columns: new[] { "DepthFrom", "DepthTo" });

            // Index for image classification and status filtering
            migrationBuilder.CreateIndex(
                name: "IX_Images_Classification_Performance",
                table: "AbpImages",
                columns: new[] { "ImageClass", "Type", "ImageStatus" });

            // Index for image type and subtype filtering
            migrationBuilder.CreateIndex(
                name: "IX_Images_TypeSubtype_Performance",
                table: "AbpImages",
                columns: new[] { "ImageTypeId", "ImageSubtypeId" });

            // Index for standard type filtering
            migrationBuilder.CreateIndex(
                name: "IX_Images_StandardType_Performance",
                table: "AbpImages",
                column: "StandardType");

            // Index for creation time sorting (default sort when no custom sort specified)
            migrationBuilder.CreateIndex(
                name: "IX_Images_CreationTime_Performance",
                table: "AbpImages",
                column: "CreationTime");

            // Composite index for default sorting pattern (Project.Name, DrillHole.Name, DepthFrom)
            // Note: This will be handled by foreign key indexes and the depth index above

            // DrillHoles table indexes for tenant-based queries and name searches
            migrationBuilder.CreateIndex(
                name: "IX_DrillHoles_TenantId_Name_Performance",
                table: "AbpDrillHoles",
                columns: new[] { "TenantId", "Name" });

            migrationBuilder.CreateIndex(
                name: "IX_DrillHoles_TenantId_ProjectId_Performance",
                table: "AbpDrillHoles",
                columns: new[] { "TenantId", "ProjectId", "ProspectId" });

            // Projects table indexes for active project filtering
            migrationBuilder.CreateIndex(
                name: "IX_Projects_TenantId_IsActive_Performance",
                table: "AbpProjects",
                columns: new[] { "TenantId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_Projects_Name_Performance",
                table: "AbpProjects",
                column: "Name");

            // Prospects table indexes
            migrationBuilder.CreateIndex(
                name: "IX_Prospects_TenantId_ProjectId_Performance",
                table: "AbpProspects",
                columns: new[] { "TenantId", "ProjectId", "IsActive" });

            migrationBuilder.CreateIndex(
                name: "IX_Prospects_Name_Performance",
                table: "AbpProspects",
                column: "Name");

            // Files table index for image file relationships
            migrationBuilder.CreateIndex(
                name: "IX_Files_ImageId_Performance",
                table: "AbpFiles",
                column: "ImageId");

            // ImageCrops table indexes for cropped image queries
            migrationBuilder.CreateIndex(
                name: "IX_ImageCrops_ImageId_DepthFrom_Performance",
                table: "AbpImageCrops",
                columns: new[] { "ImageId", "DepthFrom" });

            migrationBuilder.CreateIndex(
                name: "IX_ImageCrops_Type_Performance",
                table: "AbpImageCrops",
                column: "Type");

            // RockLines table index for rock line queries within cropped images
            migrationBuilder.CreateIndex(
                name: "IX_RockLines_ImageCropId_Performance",
                table: "AbpRockLines",
                column: "ImageCropId");

            // ImageTypes and ImageSubtypes indexes for reference data
            migrationBuilder.CreateIndex(
                name: "IX_ImageTypes_TenantId_Performance",
                table: "AbpImageTypes",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_ImageSubtypes_TenantId_Performance",
                table: "AbpImageSubtypes",
                column: "TenantId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop all performance indexes in reverse order
            migrationBuilder.DropIndex(
                name: "IX_ImageSubtypes_TenantId_Performance",
                table: "AbpImageSubtypes");

            migrationBuilder.DropIndex(
                name: "IX_ImageTypes_TenantId_Performance",
                table: "AbpImageTypes");

            migrationBuilder.DropIndex(
                name: "IX_RockLines_ImageCropId_Performance",
                table: "AbpRockLines");

            migrationBuilder.DropIndex(
                name: "IX_ImageCrops_Type_Performance",
                table: "AbpImageCrops");

            migrationBuilder.DropIndex(
                name: "IX_ImageCrops_ImageId_DepthFrom_Performance",
                table: "AbpImageCrops");

            migrationBuilder.DropIndex(
                name: "IX_Files_ImageId_Performance",
                table: "AbpFiles");

            migrationBuilder.DropIndex(
                name: "IX_Prospects_Name_Performance",
                table: "AbpProspects");

            migrationBuilder.DropIndex(
                name: "IX_Prospects_TenantId_ProjectId_Performance",
                table: "AbpProspects");

            migrationBuilder.DropIndex(
                name: "IX_Projects_Name_Performance",
                table: "AbpProjects");

            migrationBuilder.DropIndex(
                name: "IX_Projects_TenantId_IsActive_Performance",
                table: "AbpProjects");

            migrationBuilder.DropIndex(
                name: "IX_DrillHoles_TenantId_ProjectId_Performance",
                table: "AbpDrillHoles");

            migrationBuilder.DropIndex(
                name: "IX_DrillHoles_TenantId_Name_Performance",
                table: "AbpDrillHoles");

            migrationBuilder.DropIndex(
                name: "IX_Images_CreationTime_Performance",
                table: "AbpImages");

            migrationBuilder.DropIndex(
                name: "IX_Images_StandardType_Performance",
                table: "AbpImages");

            migrationBuilder.DropIndex(
                name: "IX_Images_TypeSubtype_Performance",
                table: "AbpImages");

            migrationBuilder.DropIndex(
                name: "IX_Images_Classification_Performance",
                table: "AbpImages");

            migrationBuilder.DropIndex(
                name: "IX_Images_DepthRange_Performance",
                table: "AbpImages");

            migrationBuilder.DropIndex(
                name: "IX_Images_TenantId_DrillHoleId_Performance",
                table: "AbpImages");
        }
    }
}
