// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using aibase.EntityFrameworkCore;

#nullable disable

namespace aibase.Migrations
{
    [DbContext(typeof(aibaseDbContext))]
    [Migration("20250731170000_AddImageServicePerformanceIndexes")]
    partial class AddImageServicePerformanceIndexes
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
            // This is a partial migration that only adds indexes
            // The model structure remains the same as the previous migration
            // This designer file is auto-generated and will be updated by EF Core
        }
    }
}
